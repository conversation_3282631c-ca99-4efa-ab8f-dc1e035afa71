const axios = require('axios')
const { customLog } = require('./logger')

// Configuración del cliente HTTP para el backend personalizado
const axiosInstance = axios.create({
  baseURL: process.env.AUDIO_BACKEND_BASE_URL,
  headers: {
    Authorization: process.env.AUDIO_BACKEND_API_KEY
  },
  timeout: 30000 // 30 segundos
})

/**
 * Servicio de Speech-to-Text usando el backend personalizado
 */
class SpeechToTextService {
  constructor() {
    this.isAvailable = false
    // No llamamos initialize() automáticamente, se debe llamar explícitamente
  }

  /**
   * Inicializa el servicio verificando la conectividad
   */
  async initialize() {
    try {
      if (!process.env.AUDIO_BACKEND_BASE_URL || !process.env.AUDIO_BACKEND_API_KEY) {
        customLog('⚠️ Speech-to-Text backend not configured. Missing AUDIO_BACKEND_BASE_URL or AUDIO_BACKEND_API_KEY')
        return
      }

      // Verificar conectividad con el backend
      await this.healthCheck()
      this.isAvailable = true
      customLog('✅ Custom Speech-to-Text service initialized successfully')
    } catch (error) {
      customLog('❌ Failed to initialize Speech-to-Text service:', {
        message: error.message,
        code: error.code || 'NO_CODE'
      })
      this.isAvailable = false
    }
  }

  /**
   * Verifica la salud del servicio backend
   */
  async healthCheck() {
    try {
      // Hacer una petición de prueba al endpoint s2t con datos mínimos
      const testPayload = {
        audio_data: "", // Audio vacío para test
        config: {
          language: 'es-ES',
          model: 'latest_long',
          enhanced: true,
          sample_rate: 16000,
          encoding: 'LINEAR16'
        }
      }

      const response = await axiosInstance.post('/s2t', testPayload, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 5000
      })

      // Si llegamos aquí, el servicio está disponible (incluso si devuelve error por audio vacío)
      return true
    } catch (error) {
      // Si es un error 422 (datos inválidos), el servicio está funcionando
      if (error.response?.status === 422) {
        return true
      }

      // Para otros errores, el servicio no está disponible
      throw new Error(`Backend not reachable: ${error.message}`)
    }
  }

  /**
   * Convierte audio a texto usando el backend personalizado
   * @param {Buffer} audioBuffer - Buffer de audio
   * @param {Object} options - Opciones de configuración
   */
  async transcribeAudio(audioBuffer, options = {}) {
    if (!this.isAvailable) {
      throw new Error('Speech-to-Text service not available')
    }

    try {
      // Crear FormData para enviar el audio como archivo
      const FormData = require('form-data')
      const formData = new FormData()

      // Agregar el archivo de audio
      formData.append('file', audioBuffer, {
        filename: 'audio.wav',
        contentType: 'audio/wav'
      })

      // Agregar los parámetros como JSON
      const params = {
        language: options.language || 'es-ES',
        model: options.model || 'latest_long',
        enhanced: options.enhanced || true,
        sample_rate: options.sampleRate || 16000,
        encoding: options.encoding || 'LINEAR16'
      }
      formData.append('params', JSON.stringify(params))

      const response = await axiosInstance.post('/s2t', formData, {
        headers: {
          ...formData.getHeaders()
        }
      })

      return {
        transcript: response.data.transcript || response.data.text || response.data.transcription || '',
        confidence: response.data.confidence || 1.0,
        isFinal: true
      }
    } catch (error) {
      customLog('❌ Error transcribing audio with custom backend:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      })
      throw error
    }
  }

  /**
   * Procesa audio en streaming (simulado con chunks)
   * @param {Buffer} audioChunk - Chunk de audio
   * @param {Function} onTranscript - Callback para transcripciones
   * @param {Function} onError - Callback para errores
   */
  async processAudioChunk(audioChunk, onTranscript, onError) {
    try {
      const result = await this.transcribeAudio(audioChunk)
      if (result.transcript && result.transcript.trim()) {
        onTranscript(result)
      }
    } catch (error) {
      onError(error)
    }
  }

  /**
   * Verifica si el servicio está disponible
   */
  isServiceAvailable() {
    return this.isAvailable
  }

  /**
   * Reinicia el servicio
   */
  async restart() {
    this.isAvailable = false
    await this.initialize()
  }
}

// Crear instancia singleton
const speechToTextService = new SpeechToTextService()

module.exports = { speechToTextService }
