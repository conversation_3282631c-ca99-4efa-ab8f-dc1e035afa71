const axios = require('axios')
const { customLog } = require('./logger')

// Configuración del cliente HTTP para el backend personalizado
const axiosInstance = axios.create({
  baseURL: process.env.AUDIO_BACKEND_BASE_URL,
  headers: {
    Authorization: process.env.AUDIO_BACKEND_API_KEY
  },
  timeout: 30000 // 30 segundos
})

/**
 * Servicio de Speech-to-Text usando el backend personalizado
 */
class SpeechToTextService {
  constructor() {
    this.isAvailable = false
    this.initialize()
  }

  /**
   * Inicializa el servicio verificando la conectividad
   */
  async initialize() {
    try {
      if (!process.env.AUDIO_BACKEND_BASE_URL || !process.env.AUDIO_BACKEND_API_KEY) {
        customLog('⚠️ Speech-to-Text backend not configured. Missing AUDIO_BACKEND_BASE_URL or AUDIO_BACKEND_API_KEY')
        return
      }

      // Verificar conectividad con el backend
      await this.healthCheck()
      this.isAvailable = true
      customLog('✅ Custom Speech-to-Text service initialized successfully')
    } catch (error) {
      customLog('❌ Failed to initialize Speech-to-Text service:', {
        message: error.message,
        code: error.code || 'NO_CODE'
      })
      this.isAvailable = false
    }
  }

  /**
   * Verifica la salud del servicio backend
   */
  async healthCheck() {
    try {
      // Intentar hacer una petición simple para verificar conectividad
      const response = await axiosInstance.get('/health', { timeout: 5000 })
      return response.status === 200
    } catch (error) {
      // Si no hay endpoint de health, intentar con el endpoint principal
      try {
        await axiosInstance.get('/', { timeout: 5000 })
        return true
      } catch (secondError) {
        throw new Error(`Backend not reachable: ${error.message}`)
      }
    }
  }

  /**
   * Convierte audio a texto usando el backend personalizado
   * @param {Buffer} audioBuffer - Buffer de audio
   * @param {Object} options - Opciones de configuración
   */
  async transcribeAudio(audioBuffer, options = {}) {
    if (!this.isAvailable) {
      throw new Error('Speech-to-Text service not available')
    }

    try {
      // Usar el endpoint de speech-to-text con el formato que espera el backend
      const payload = {
        audio_data: audioBuffer.toString('base64'),
        config: {
          language: options.language || 'es-ES',
          model: options.model || 'latest_long',
          enhanced: options.enhanced || true,
          sample_rate: options.sampleRate || 16000,
          encoding: options.encoding || 'LINEAR16'
        }
      }

      const response = await axiosInstance.post('/s2t', payload, {
        headers: {
          'Content-Type': 'application/json'
        }
      })

      return {
        transcript: response.data.transcript || response.data.text || response.data.transcription || '',
        confidence: response.data.confidence || 1.0,
        isFinal: true
      }
    } catch (error) {
      customLog('❌ Error transcribing audio with custom backend:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      })
      throw error
    }
  }

  /**
   * Procesa audio en streaming (simulado con chunks)
   * @param {Buffer} audioChunk - Chunk de audio
   * @param {Function} onTranscript - Callback para transcripciones
   * @param {Function} onError - Callback para errores
   */
  async processAudioChunk(audioChunk, onTranscript, onError) {
    try {
      const result = await this.transcribeAudio(audioChunk)
      if (result.transcript && result.transcript.trim()) {
        onTranscript(result)
      }
    } catch (error) {
      onError(error)
    }
  }

  /**
   * Verifica si el servicio está disponible
   */
  isServiceAvailable() {
    return this.isAvailable
  }

  /**
   * Reinicia el servicio
   */
  async restart() {
    this.isAvailable = false
    await this.initialize()
  }
}

// Crear instancia singleton
const speechToTextService = new SpeechToTextService()

module.exports = { speechToTextService }
