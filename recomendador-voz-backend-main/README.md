<h1 align="center">
  MHC Voice Recommendator Backend
</h1>

<h4 align="center">Backend service for a voice-based AI recommendation system</h4>
<br>

# Prerequisites

- To develop:
  - [Node.js](https://nodejs.org/) (v18 or higher)
  - [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
  - [Visual Studio Code](https://code.visualstudio.com/)
  - [Yarn](https://classic.yarnpkg.com/lang/en/docs/install/)
  - [Prettier Formatter for VS Code](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode). Formatting rules at `.prettierrc.cjs`

<br>

# How To Use for Development

To clone and run this application, make sure you have all the [prerequisites](#prerequisites) installed on your computer.

From your command line:

```bash
# Clone this repository
$ git clone [repository-url]

# Go into the repository
$ cd recomendador-voz-backend

# Install dependencies
$ yarn install

# Setup .env file

# Run the server in development mode
$ yarn dev
```

<br>

# Build and Deploy

To deploy this application to Google Cloud Run, from your command line:

```bash
# Build and deploy to Google Cloud Run
$ yarn deploy
```

<br>

# Custom Speech-to-Text Backend Configuration

This application uses a custom Speech-to-Text backend for voice recognition. The speech recognition service is already configured to use your custom backend.

## Configuration

The application automatically uses the custom backend configured in your `.env` file:

```
AUDIO_BACKEND_BASE_URL="https://dev.dl2discovery.org/sts/api/v1"
AUDIO_BACKEND_API_KEY="Bearer 502a8de4-b1f9-41a1-8312-2fe403134690"
```

## Speech Recognition Features

- **Real-time audio processing**: Audio chunks are buffered and processed efficiently
- **Automatic retry logic**: Transient errors are automatically retried with exponential backoff
- **Error recovery**: The system gracefully handles backend connectivity issues
- **Buffer management**: Audio data is intelligently buffered to optimize processing

## Fallback Mode

If the custom backend is not reachable, the application will run in fallback mode with speech recognition disabled. The application will log clear messages about the backend status and continue to work for other features.

<br>

# System Architecture

The system consists of the following main components:

- **WebSocket Server**: Handles real-time communication with clients using Socket.IO
- **Speech-to-Text Service**: Uses custom backend API to convert audio input to text
- **AI**: Uses custom AI service to process text queries and generate responses
- **Text-to-Speech Service**: Uses custom Speech Tools Server to convert AI responses to speech
