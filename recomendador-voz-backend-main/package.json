{"name": "recomendador-voz-backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development node --watch index.js", "start": "cross-env NODE_ENV=production node index.js", "deploy": "gcloud run deploy --source ."}, "engines": {"node": ">=18.x"}, "dependencies": {"@google-cloud/speech": "^6.7.0", "axios": "^1.8.4", "better-logging": "^5.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-env": "^7.0.3", "csurf": "^1.11.0", "dotenv": "^16.4.7", "express": "^4.21.2", "hpp": "^0.2.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}}