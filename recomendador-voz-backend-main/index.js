const express = require('express')
const { createServer } = require('http')
const { Server } = require('socket.io')
const cors = require('cors')
const hpp = require('hpp')
const config = require('./modules/config')
const { createAIService, generateAIReply } = require('./modules/AIService')
const { generateSpeech } = require('./modules/voiceService')
const { speechToTextService } = require('./modules/speechToTextService')
const { customLog } = require('./modules/logger')
const { cleanTextForSpeech } = require('./modules/textProcessor')
const { setSecurityHeaders, validateApiKey, configureSocketSecurity, configureCors } = require('./modules/security')
const cookieParser = require('cookie-parser')
const csurf = require('csurf')
const csrfProtection = csurf({ cookie: true })

require('better-logging')(console)

const app = express()
app.disable('x-powered-by')
const httpServer = createServer(app)

setSecurityHeaders(app)

app.use(hpp())
app.use(express.json())
configureCors(app)
app.use(cookieParser())
app.use(csrfProtection)

app.use('/api', validateApiKey)

const io = new Server(httpServer, {
  cors: {
    origin:
      process.env.NODE_ENV === 'production'
        ? [process.env.FRONTEND_DEV_URL, process.env.FRONTEND_PRO_URL]
        : ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:5174'],
    methods: ['GET', 'POST'],
    credentials: true
  }
})
configureSocketSecurity(io)

createAIService()

// Initialize custom Speech-to-Text service
let speechRecognitionAvailable = false

// Función asíncrona para inicializar el servicio de speech-to-text
async function initializeSpeechService() {
  try {
    if (config.HAS_CUSTOM_SPEECH_BACKEND) {
      // Esperar a que el servicio se inicialice completamente
      await speechToTextService.initialize()
      speechRecognitionAvailable = speechToTextService.isServiceAvailable()

      if (speechRecognitionAvailable) {
        customLog('✅ Custom Speech-to-Text service initialized successfully')
      } else {
        customLog('⚠️ Custom Speech-to-Text backend not reachable. Speech recognition will be disabled.')
      }
    } else {
      customLog('⚠️ Custom Speech-to-Text backend not configured. Speech recognition will be disabled.')
      customLog('💡 To enable speech recognition, please configure AUDIO_BACKEND_BASE_URL and AUDIO_BACKEND_API_KEY in your .env file')
    }
  } catch (error) {
    customLog('❌ Failed to initialize Speech-to-Text service:', {
      message: error.message,
      code: error.code || 'NO_CODE'
    })
    speechRecognitionAvailable = false
  }
}

// Inicializar el servicio de forma asíncrona
initializeSpeechService()

io.on('connection', (socket) => {
  customLog('Client connected:', socket.id)

  // Notify client about speech recognition availability
  socket.emit('service_status', {
    speechRecognition: speechRecognitionAvailable,
    message: speechRecognitionAvailable
      ? 'Speech recognition is available'
      : 'Speech recognition is disabled - missing Google Cloud credentials'
  })

  let conversationHistory = []
  let isMuted = false
  let isProcessingAudio = false
  let inactivityTimeout = null
  let retryCount = 0
  let maxRetries = 3
  let retryDelay = 1000 // Start with 1 second delay
  let audioBuffer = []
  let bufferTimeout = null
  let lastTranscriptionTime = Date.now()

  const processAudioBuffer = async () => {
    if (!speechRecognitionAvailable || !speechToTextService.isServiceAvailable()) {
      customLog('❌ Speech recognition not available - custom backend not configured')
      socket.emit('error', {
        type: 'service_unavailable',
        message: 'Speech recognition service is not configured. Please contact support.'
      })
      return
    }

    if (audioBuffer.length === 0 || isProcessingAudio) {
      return
    }

    isProcessingAudio = true
    customLog('Processing audio buffer with custom backend')

    try {
      // Combinar todos los chunks de audio en un solo buffer
      const combinedBuffer = Buffer.concat(audioBuffer)
      audioBuffer = [] // Limpiar el buffer

      // Procesar con el servicio personalizado
      await speechToTextService.processAudioChunk(
        combinedBuffer,
        (result) => {
          // Callback para transcripción exitosa
          handleTranscriptionResult(result)
        },
        (error) => {
          // Callback para errores
          handleTranscriptionError(error)
        }
      )
    } catch (error) {
      handleTranscriptionError(error)
    } finally {
      isProcessingAudio = false
    }
  }

  const handleTranscriptionResult = (result) => {
    try {
      // Reset retry count on successful transcription
      retryCount = 0
      retryDelay = 1000
      lastTranscriptionTime = Date.now()

      resetInactivityTimer()

      const transcription = result.transcript.trim()
      if (transcription) {
        socket.emit('transcription', transcription)

        // Procesar transcripción final
        if (result.isFinal && transcription !== '') {
          processTranscription(transcription)
        }
      }
    } catch (error) {
      customLog('❌ Error handling transcription result:', error.message)
    }
  }

  const handleTranscriptionError = (error) => {
    // Enhanced error logging with more details
    customLog('Error transcribing audio:', {
      message: error.message || 'Unknown error',
      code: error.code || 'NO_CODE',
      timestamp: new Date().toISOString()
    })

    // Check for specific backend errors
    if (error.message?.includes('authentication') || error.message?.includes('401')) {
      customLog('❌ Backend authentication error detected. Please check your API key configuration.')
      socket.emit('error', {
        type: 'authentication',
        message: 'Speech recognition service authentication failed. Please check server configuration.'
      })
      retryCount = maxRetries // Don't retry authentication errors
    } else if (error.message?.includes('configuration') || error.message?.includes('400')) {
      customLog('❌ Invalid configuration for custom Speech API')
      socket.emit('error', {
        type: 'configuration',
        message: 'Speech recognition configuration error.'
      })
      retryCount = maxRetries // Don't retry configuration errors
    } else {
      customLog('❌ General speech recognition error:', error.message || 'Unknown error')

      // Implement retry logic for transient errors
      if (retryCount < maxRetries) {
        retryCount++
        customLog(`🔄 Attempting to restart speech recognition (retry ${retryCount}/${maxRetries}) in ${retryDelay}ms`)

        setTimeout(async () => {
          if (speechRecognitionAvailable && speechToTextService.isServiceAvailable()) {
            await speechToTextService.restart()
            customLog('✅ Speech recognition service restarted successfully')
            retryDelay = Math.min(retryDelay * 2, 10000) // Exponential backoff, max 10 seconds
          }
        }, retryDelay)
      } else {
        customLog('❌ Max retries reached, disabling speech recognition for this session')
        socket.emit('error', {
          type: 'max_retries',
          message: 'Speech recognition temporarily unavailable after multiple attempts.'
        })
      }
    }
  }

  const processTranscription = async (transcription) => {
    try {
      socket.emit('loading')

      if (transcription.toLowerCase() === 'ok aura') {
        socket.emit('reply', { text: '[OK AURA]', audioUrl: null })
        return
      }

      // Remove "ok aura" prefix if present
      if (transcription.toLowerCase().startsWith('ok aura')) {
        transcription = transcription.slice(7).trim()
      }

      if (transcription.length > config.MAX_USER_INPUT_LENGTH) {
        customLog(`Warning: Truncating long user input from ${transcription.length} to ${config.MAX_USER_INPUT_LENGTH} characters`)
        transcription = transcription.substring(0, config.MAX_USER_INPUT_LENGTH)
      }

      conversationHistory.push({ user: true, content: transcription })
      customLog(`User: ${transcription}`)

      const reply = await generateAIReply(conversationHistory)
      customLog(`AI: ${reply}`)
      conversationHistory.push({ user: false, content: reply })

      if (
        reply.startsWith('[JSON]') ||
        reply.startsWith('[NONE]') ||
        reply.startsWith('[COMMAND]') ||
        reply.startsWith('[PAUSE]') ||
        reply.startsWith('[EXIT]')
      ) {
        socket.emit('reply', { text: reply, audioUrl: null })
      } else {
        const audioUrl = !isMuted
          ? `data:audio/mpeg;base64,${(await generateSpeech(cleanTextForSpeech(reply))).toString('base64')}`
          : null
        socket.emit('reply', { text: reply, audioUrl })
      }
    } catch (error) {
      customLog('❌ Error processing transcription:', error.message)
      socket.emit('error', {
        type: 'processing',
        message: 'Error processing your request. Please try again.'
      })
    }
  }

  const resetInactivityTimer = () => {
    if (inactivityTimeout) {
      clearTimeout(inactivityTimeout)
    }

    inactivityTimeout = setTimeout(() => {
      if (audioBuffer.length > 0) {
        customLog('Clearing audio buffer due to inactivity')
        audioBuffer = []
      }
      if (bufferTimeout) {
        clearTimeout(bufferTimeout)
        bufferTimeout = null
      }
    }, 60000) // 1 minute
  }

  const scheduleBufferProcessing = () => {
    if (bufferTimeout) {
      clearTimeout(bufferTimeout)
    }

    // Process buffer after 500ms of silence or when it gets too large
    bufferTimeout = setTimeout(() => {
      if (audioBuffer.length > 0) {
        processAudioBuffer()
      }
    }, 500)
  }

  socket.on('audio', (audioData) => {
    if (!speechRecognitionAvailable) {
      // Silently ignore audio data if speech recognition is not available
      return
    }

    try {
      const buffer = Buffer.from(new Uint8Array(audioData))

      // Add to audio buffer
      audioBuffer.push(buffer)

      // Reset inactivity timer
      resetInactivityTimer()

      // Schedule buffer processing
      scheduleBufferProcessing()

      // Process immediately if buffer gets too large (prevent memory issues)
      const totalBufferSize = audioBuffer.reduce((total, buf) => total + buf.length, 0)
      if (totalBufferSize > 1024 * 1024) { // 1MB limit
        customLog('Audio buffer size limit reached, processing immediately')
        if (bufferTimeout) {
          clearTimeout(bufferTimeout)
          bufferTimeout = null
        }
        processAudioBuffer()
      }

    } catch (error) {
      customLog('Error processing audio data:', {
        message: error.message || 'Unknown audio processing error',
        timestamp: new Date().toISOString()
      })

      socket.emit('error', {
        type: 'audio_processing',
        message: 'Error processing audio data.'
      })
    }
  })

  socket.on('disconnect', () => {
    customLog('Client disconnected:', socket.id)

    // Clean up audio buffer and timers
    audioBuffer = []
    if (bufferTimeout) {
      clearTimeout(bufferTimeout)
      bufferTimeout = null
    }
    if (inactivityTimeout) {
      clearTimeout(inactivityTimeout)
      inactivityTimeout = null
    }

    // Reset processing state
    isProcessingAudio = false
  })

  socket.on('mute-sound', () => {
    isMuted = true
    customLog(`Sound muted for client: ${socket.id}`)
  })

  socket.on('unmute-sound', () => {
    isMuted = false
    customLog(`Sound unmuted for client: ${socket.id}`)
  })

  // Initialize speech recognition if available
  if (speechRecognitionAvailable) {
    customLog('✅ Speech recognition ready for client:', socket.id)
    resetInactivityTimer()
  } else {
    customLog('⚠️ Speech recognition not available for client:', socket.id)
  }
})

httpServer.listen(config.PORT, () => {
  customLog(`Server running on port ${config.PORT}`)
})
