import { useState, useEffect, useRef } from "react";
import { Image, Mic, Modal } from "microapps";
import { io, Socket } from "socket.io-client";
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext";
import { useSimpleStreamingSpeech } from '../../hooks/useStreamingSpeech';
import { useQuestionsColor } from "../../../utils/questionsColorSystem";
import { cluesStorage } from "../../../services/ai/storage/CluesStorage";
import "./PlayView.scss";

interface CountdownMessages {
  questionsCountdownMessages: Record<string, string>;
}

interface PlayViewProps {
  handleShowClues: () => void;
  handleExistGame: () => void;
  showExitPopup: boolean;
  handleConfirmExit: () => void;
  handleCancelExit: () => void;
}

interface SessionMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

const PlayView: React.FC<PlayViewProps> = ({
  handleShowClues,
  handleExistGame,
  showExitPopup,
  handleConfirmExit,
  handleCancelExit,
}) => {
  const { session, askQuestion, askInitialMessage, questionsRemaining } = useEnygmaGame();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLivesPopUpShown, setIsLivesPopUpShown] = useState(false);
  const [showClueTooltip, setShowClueTooltip] = useState(false);
  const [currentClue, setCurrentClue] = useState("");
  const [isClueButtonVibrating, setIsClueButtonVibrating] = useState(false);
  const [isClueFadingOut, setIsClueFadingOut] = useState(false);
  const [lastShownClueId, setLastShownClueId] = useState<string | null>(null);
  const [countdownMessages, setCountdownMessages] = useState<CountdownMessages | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Hook para obtener el color basado en preguntas restantes
  const questionsColorData = useQuestionsColor(
    session?.maxQuestions || 20,
    session?.questionCount || 0
  );

  // Cargar mensajes de cuenta regresiva
  useEffect(() => {
    const loadCountdownMessages = async () => {
      try {
        const response = await fetch('/questions-countdown-messages.json');
        const data: CountdownMessages = await response.json();
        setCountdownMessages(data);
      } catch (error) {
        console.error('Error loading countdown messages:', error);
      }
    };

    loadCountdownMessages();
  }, []);

  // Función para obtener el mensaje de cuenta regresiva
  const getCountdownMessage = (remainingQuestions: number): string | null => {
    if (!countdownMessages) return null;

    const messageKey = remainingQuestions.toString();
    return countdownMessages.questionsCountdownMessages[messageKey] || null;
  };

  // Voice states
  const [micLevel, setMicLevel] = useState(0);
  const [isVoiceActive, setIsVoiceActive] = useState(false);
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [isSocketConnected, setIsSocketConnected] = useState(false);
  const [isAILoading, setIsAILoading] = useState(false);

  // References
  const socketRef = useRef<Socket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const playbackSourceRef = useRef<AudioBufferSourceNode | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);

  // Audio functions
  const stopAudio = () => {
    if (playbackSourceRef.current) {
      try {
        playbackSourceRef.current.stop();
        playbackSourceRef.current = null;
      } catch (error) {
        console.error("Error stopping audio:", error);
      }
    }
  };

  const playAudio = async (audioUrl: string) => {
    if (!audioContextRef.current) return;

    try {
      const response = await fetch(audioUrl);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);

      if (playbackSourceRef.current) {
        stopAudio();
      }

      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContextRef.current.destination);
      playbackSourceRef.current = source;
      source.start();

      source.onended = () => {
        playbackSourceRef.current = null;
      };
    } catch (error) {
      console.error("Error playing audio:", error);
    }
  };

  const setupTranscription = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          sampleSize: 16,
        },
      });

      audioContextRef.current = new AudioContext();
      mediaStreamRef.current = stream;

      await audioContextRef.current.audioWorklet.addModule('/audio-processor.js');

      const source = audioContextRef.current.createMediaStreamSource(stream);
      const processor = new AudioWorkletNode(audioContextRef.current, 'audio-processor');

      const audioBuffer: number[] = [];

      processor.port.onmessage = (event) => {
        const audioSamples = event.data;
        const int16Buffer = new Int16Array(audioSamples.length);

        for (let i = 0; i < audioSamples.length; i++) {
          int16Buffer[i] = Math.max(-32768, Math.min(32767, audioSamples[i] * 32768));
        }

        audioBuffer.push(...int16Buffer);

        if (audioBuffer.length >= 1600) { // 100ms chunks
          const chunkToSend = new Int16Array(audioBuffer.splice(0, 1600));
          if (socketRef.current?.connected) {
            socketRef.current.emit('audio', chunkToSend.buffer);
          }
        }
      };

      source.connect(processor);

      const analyser = audioContextRef.current.createAnalyser();
      analyser.fftSize = 2048;
      source.connect(analyser);

      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      const threshold = 0.1;

      const updateMicLevel = () => {
        if (!audioContextRef.current) return;

        analyser.getByteTimeDomainData(dataArray);
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          const v = (dataArray[i] - 128) / 128;
          sum += v * v;
        }
        const rms = Math.sqrt(sum / dataArray.length);
        setMicLevel(rms);

        if (rms > threshold && playbackSourceRef.current) {
          stopAudio();
        }

        requestAnimationFrame(updateMicLevel);
      };

      updateMicLevel();

      setIsVoiceActive(true);
      setVoiceError(null);

    } catch (error) {
      console.error("Error setting up transcription:", error);
      setVoiceError(error instanceof Error ? error.message : "Audio setup error");
      setIsVoiceActive(false);
    }
  };

  const stopTranscription = async () => {
    try {
      setIsVoiceActive(false);

      if (playbackSourceRef.current) {
        stopAudio();
      }

      if (mediaStreamRef.current) {
        const tracks = mediaStreamRef.current.getTracks();
        for (const track of tracks) {
          track.enabled = false;
          setTimeout(() => {
            track.stop();
          }, 50);
        }
        mediaStreamRef.current = null;
      }

      if (audioContextRef.current) {
        if (audioContextRef.current.state !== 'closed' && audioContextRef.current.state !== 'suspended') {
          await audioContextRef.current.suspend();
        }

        await new Promise((resolve) => setTimeout(resolve, 100));

        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
          await audioContextRef.current.close();
        }

        audioContextRef.current = null;
      }

    } catch (error) {
      console.error("Error stopping transcription:", error);
    }
  };

  const connectSocket = async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      const apiUrl = import.meta.env.VITE_FLUID_VOICE_API_URL;
      const apiKey = import.meta.env.VITE_FLUID_VOICE_API_KEY;

      if (!apiUrl || !apiKey) {
        const error = new Error("Environment variables not configured");
        reject(error);
        return;
      }

      try {
        socketRef.current = io(apiUrl, {
          auth: {
            apiKey: apiKey,
          },
          transports: ['websocket', 'polling'],
        });

        if (socketRef.current) {
          setupSocketListeners(socketRef.current);

          socketRef.current.on("connect", () => {
            setIsSocketConnected(true);
            resolve();
          });

          socketRef.current.on("connect_error", (error) => {
            setIsSocketConnected(false);
            reject(error);
          });

          socketRef.current.on("disconnect", () => {
            setIsSocketConnected(false);
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  };

  const setupSocketListeners = (socket: Socket) => {
    socket.on('transcription', (newTranscription: string) => {
      if (playbackSourceRef.current) {
        stopAudio();
      }

      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        text: newTranscription,
        sender: "user",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, userMessage]);
    });

    socket.on('loading', () => {
      setIsAILoading(true);
      stopTranscription();
    });

    socket.on('reply', async ({ text, audioUrl }: { text: string; audioUrl?: string }) => {
      setIsAILoading(false);

      if (text.startsWith('[EXIT]')) {
        // Handle exit command
      } else if (text.startsWith('[PAUSE]')) {
        setIsVoiceActive(false);
      } else if (text.startsWith('[COMMAND]')) {
        // Handle command
      } else if (text.startsWith('[NONE]')) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        await setupTranscription();
      } else if (text === '[OK AURA]') {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        await setupTranscription();
      } else if (text.startsWith('[JSON]')) {
        // Handle JSON response
      } else {
        const aiMessage: ChatMessage = {
          id: `ai-${Date.now()}`,
          text: text,
          sender: "ai",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, aiMessage]);

        if (audioUrl) {
          await playAudio(audioUrl);
        }

        await new Promise((resolve) => setTimeout(resolve, 1000));
        await setupTranscription();
      }
    });

    socket.on('error', (error) => {
      console.error("Socket error:", error);
    });

    socket.on('connect_error', (error) => {
      console.error("Connection error:", error);
    });

    socket.on('disconnect', () => {
      setIsSocketConnected(false);
    });

    socket.on('reconnect', () => {
      setIsSocketConnected(true);
      setVoiceError(null);
    });

    socket.on('service_status', (status) => {
      console.log('📊 Service status received:', status);
      if (status.speechRecognition) {
        console.log('✅ Speech recognition is available');
        setVoiceError(null);
      } else {
        console.log('⚠️ Speech recognition is not available:', status.message);
        setVoiceError(status.message || 'Speech recognition not available');
      }
    });
  };



  useEffect(() => {
    const initialize = async () => {
      try {
        await connectSocket();

        setTimeout(async () => {
          try {
            await setupTranscription();
          } catch (audioError) {
            console.error("Error setting up audio:", audioError);
          }
        }, 2000);

      } catch (error) {
        console.error("Initialization error:", error);
        setVoiceError(error instanceof Error ? error.message : "Initialization error");
      }
    };

    initialize();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      stopTranscription();
    };
  }, []);

  const toggleVoice = async () => {
    if (isVoiceActive) {
      await stopTranscription();
    } else {
      await setupTranscription();
    }
  };

  // Auto-scroll
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Monitorear pistas automáticamente cuando se generan nuevas
  useEffect(() => {
    if (session?.messages && session.messages.length > 0) {
      const lastMessage = session.messages[session.messages.length - 1];

      // Solo procesar mensajes de la IA
      if (lastMessage.sender === "ai") {
        try {
          // Obtener la pista más reciente usando CluesStorage
          const recentClues = cluesStorage.getRecent(1);

          if (recentClues.length > 0) {
            const latestClue = recentClues[0];

            // Verificar si es una pista nueva que no hemos mostrado
            if (latestClue.id !== lastShownClueId && latestClue.timestamp) {
              // Verificar si la pista es reciente (dentro de los últimos 10 segundos)
              const clueTime = new Date(latestClue.timestamp).getTime();
              const messageTime = new Date(lastMessage.timestamp).getTime();

              if (Math.abs(clueTime - messageTime) < 10000 && latestClue.text) {
                showClueWithAnimation(`💡 ${latestClue.text}`);
                setLastShownClueId(latestClue.id);
              }
            }
          }
        } catch (error) {
          console.error("Error procesando pistas automáticas:", error);
        }
      }
    }
  }, [session?.messages, lastShownClueId]);

  // Sync messages from game session
  useEffect(() => {
    if (session?.messages) {
      const chatMessages: ChatMessage[] = session.messages.map(
        (msg: SessionMessage): ChatMessage => ({
          id: msg.id,
          text: msg.text,
          sender: msg.sender,
          timestamp: msg.timestamp,
        })
      );
      setMessages(chatMessages);
    }
  }, [session?.messages]);

  useEffect(() => {
    if (
      session &&
      (!session.messages || session.messages.length === 0) &&
      messages.length === 0
    ) {
      const sendInitialMessage = async () => {
        try {
          setIsLoading(true);
          await askInitialMessage("");
        } catch (error) {
          console.error("Error sending initial message:", error);
        } finally {
          setIsLoading(false);
        }
      };

      sendInitialMessage();
    }
  }, [session, messages.length, askInitialMessage]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || !session) return;

    const messageText = inputText.trim();
    setInputText("");
    setIsLoading(true);

    try {
      await askQuestion(messageText);
    } catch (error) {
      console.error("Error sending message:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Función para mostrar pista con animación
  const showClueWithAnimation = (clueText: string) => {
    console.log("🔍 Mostrando pista automática:", clueText);
    setCurrentClue(clueText);
    setShowClueTooltip(true);
    setIsClueFadingOut(false);
    setIsClueButtonVibrating(true);

    // Quitar vibración del botón después de 0.6s
    setTimeout(() => {
      setIsClueButtonVibrating(false);
    }, 600);

    // Iniciar fade out después de 4 segundos
    setTimeout(() => {
      setIsClueFadingOut(true);
    }, 4000);

    // Ocultar completamente después de 5 segundos
    setTimeout(() => {
      setShowClueTooltip(false);
      setIsClueFadingOut(false);
      setCurrentClue("");
    }, 5000);
  };

  return (
    <>
      <div className="chat-view">
        <div className="menu-left">
          <div className="enygma-logo">


            <Image
              src="assets/game/enygma.png"
              alt="Enygma"
              className="enygma-image"
              width="180px"
              aspectRatio="1:1"
            />

            <div className="speaking">
              <Mic
                level={Math.round(micLevel * 100)}
                onClick={toggleVoice}
                state={
                  isVoiceActive
                    ? "recording"
                    : voiceError
                      ? "disabled"
                      : "default"
                }
              />
              {voiceError && (
                <div
                  className="voice-error"
                  style={{ color: "red", fontSize: "12px", marginTop: "4px" }}
                >
                  {voiceError}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="chat-view-wrapper">
          <div className="chat-container">
            <div
              className={`chat-content ${
                messages.length > 0 &&
                messages[messages.length - 1].sender === "user"
                  ? "align-right"
                  : "align-left"
              }`}
            >
              <div className="chat-text body1">
                {isAILoading
                  ? "Procesando..."
                  : messages.length > 0
                    ? messages[messages.length - 1].text
                    : !isSocketConnected
                      ? "Conectando al servidor..."
                      : voiceError
                        ? `Error: ${voiceError}`
                        : isVoiceActive
                          ? "🎤 Escuchando... Habla ahora"
                          : "✅ Reconocimiento de voz listo - Activa el micrófono"}
              </div>
            </div>
          </div>

          <div className="chat-input-container">
            {/* Mensaje de cuenta regresiva */}
            {(() => {
              const countdownMessage = getCountdownMessage(questionsRemaining);
              return countdownMessage ? (
                <div className="countdown-message">
                  {countdownMessage}
                </div>
              ) : null;
            })()}

            <div className="input-wrapper">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Haz una pregunta sobre el personaje..."
                disabled={isLoading || !session}
                className="chat-input"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputText.trim() || isLoading || !session}
                className="send-button"
              >
                {isLoading ? "Enviando..." : "Enviar"}
              </button>
            </div>
          </div>
        </div>

        <div className="menu-right">
          <div
            onClick={() => setIsLivesPopUpShown((prev) => !prev)}
            className="image-button"
          >
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/lives.png"
              alt="Vidas"
              className="book-image"
            />

            {session && (
              <p
                className={`body2 bold questions-color-text ${questionsColorData.colorClass}`}
                style={{
                  color: questionsColorData.hexColor,
                }}
              >
                {questionsRemaining}
              </p>
            )}
          </div>

          <div
            onClick={handleShowClues}
            className={`image-button ${isClueButtonVibrating ? 'clues-button-vibrate' : ''}`}
          >
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/clues.png"
              alt="Pistas"
              className="clues-image"
            />
            <p className="body2 bold">Pistas</p>
          </div>

          <div onClick={handleExistGame} className="image-button">
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/exit.png"
              alt="Salir"
              className="exit-image"
            />
            <p className="body2 bold">Salir</p>
          </div>
        </div>

      </div>

      {/* Tooltip de pista - fuera del contenedor principal */}
      {showClueTooltip && (
        <div
          className={`clue-tooltip ${isClueFadingOut ? 'fade-out' : ''}`}
          style={{
            position: 'fixed',
            bottom: '2rem',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(136, 255, 213, 0.95)',
            color: '#001428',
            padding: '1.5rem 2rem',
            borderRadius: '16px',
            border: '2px solid #88FFD5',
            boxShadow: '0px 0px 20px 0px rgba(136, 255, 213, 0.6)',
            zIndex: 9999,
            maxWidth: '500px',
            textAlign: 'center',
            fontWeight: 600,
            fontSize: '1.1rem'
          }}
        >
          {currentClue}
        </div>
      )}

      {showExitPopup && (
        <Modal
          title="¿Seguro que quieres salir del juego?"
          onClose={handleCancelExit}
          onCancel={handleConfirmExit}
          onConfirm={handleCancelExit}
          cancelText="Salir de todos modos"
          confirmText="Seguir jugando"
          body="Si sales ahora, vas a perder tu progreso actual. Puedes seguir jugando o salir cuando quieras."
        />
      )}

      {isLivesPopUpShown && (
        <Modal
          title="Tus preguntas restantes"
          onClose={() => setIsLivesPopUpShown((prev) => !prev)}
          onConfirm={() => setIsLivesPopUpShown((prev) => !prev)}
          confirmText="Entendido"
          body=" Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada
          vez que haces una, se descuenta del contador. Piensa bien cada
          pregunta: ¡cada una cuenta!"
        />
      )}
    </>
  );
};

export default PlayView;
